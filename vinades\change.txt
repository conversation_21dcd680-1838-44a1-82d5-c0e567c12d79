# 27/05/2025 by <PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON> chạy bên info thì bỏ qua
ALTER TABLE `nv4_vi_bidding_bid_id`
ADD COLUMN `reoffer_room_id` INT NOT NULL DEFAULT '0' COMMENT 'ID phòng chào giá trực tuyến',
ADD COLUMN `reoffer_result_id` INT NOT NULL DEFAULT '0' COMMENT 'ID kết quả chào giá trực tuyến';
ALTER TABLE `nv4_en_bidding_bid_id`
ADD COLUMN `reoffer_room_id` INT NOT NULL DEFAULT '0' COMMENT 'ID phòng chào giá trực tuyến',
ADD COLUMN `reoffer_result_id` INT NOT NULL DEFAULT '0' COMMENT 'ID kết quả chào giá trực tuyến';

# 14/05/2025 by <PERSON><PERSON>:
ch<PERSON><PERSON> ở kho info nếu chạy ròi thì bỏ qua
ALTER TABLE nv4_vi_bidding_ycbg
ADD COLUMN address_ycbg VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Địa chỉ YCBG',
ADD COLUMN id_province INT(11) NOT NULL DEFAULT 0 COMMENT 'ID tỉnh/thành phố',
ADD COLUMN id_district INT(11) NOT NULL DEFAULT 0 COMMENT 'ID quận/huyện',
ADD COLUMN id_ward INT(11) NOT NULL DEFAULT 0 COMMENT 'ID phường/xã';
ALTER TABLE nv4_en_bidding_ycbg
ADD COLUMN address_ycbg VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Địa chỉ YCBG',
ADD COLUMN id_province INT(11) NOT NULL DEFAULT 0 COMMENT 'ID tỉnh/thành phố',
ADD COLUMN id_district INT(11) NOT NULL DEFAULT 0 COMMENT 'ID quận/huyện',
ADD COLUMN id_ward INT(11) NOT NULL DEFAULT 0 COMMENT 'ID phường/xã';
ALTER TABLE nv4_bidding_filter ADD COLUMN id_ward INT(11) NOT NULL DEFAULT 0 COMMENT 'ID phường/xã';

# 13/05/2025 by Huy: 
Chạy tools_2025/remove_foreign_copanies.sh để xóa những dữ liệu mà có doanh nghiệp nước ngoài

# 10/05/2025 by Huy: 
DELETE FROM `nv4_vi_new_msc_renewal_mkt` WHERE `short_fee_info` = '';
 
# 10/05/2025 by Huy: Bổ sung bảng csdl thống kê qua mail mkt

CREATE TABLE IF NOT EXISTS `nv4_vi_new_msc_renewal_mkt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contact_id` varchar(50) NOT NULL DEFAULT '' COMMENT 'Mã số thuế',
  `email` varchar(250) NOT NULL DEFAULT '',
  `contact_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'Loại email sẽ sử dụng để gửi mail: 1 - chưa đăng ký, 2 - đã đăng ký',
  `company_name` varchar(250) NOT NULL DEFAULT '' COMMENT 'Tên doanh nghiệp',
  `link_bids_profile` text DEFAULT NULL COMMENT 'Link tên doanh nghiệp bên dauthau.net',
  `rep_name` varchar(250) NOT NULL DEFAULT '' COMMENT 'Tên người đại diện',
  `expiry_time` varchar(30) NOT NULL DEFAULT '' COMMENT 'Thời gian hết hạn MSC',
  `expiry_time_timestamp` int(11) NOT NULL DEFAULT 0 COMMENT 'Thời gian hết hạn dạng timestamp',
  `short_fee_info` text NOT NULL COMMENT 'Thông tin phí ngắn gọn',
  `total_money` varchar(30) NOT NULL DEFAULT '' COMMENT 'Tổng tiền gia hạn',
  `username` varchar(100) NOT NULL DEFAULT '',
  `link_permission` text DEFAULT NULL COMMENT 'Link tắt nhận thông báo email',
  `add_time` int(11) NOT NULL DEFAULT 0 COMMENT 'Thời gian thêm vào',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT 'Thời gian cập nhật',
  `mail_alert_disabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Đánh dấu doanh nghiệp đã tắt nhận email',
  `renewed` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Đánh dấu doanh nghiệp đã gia hạn MSC',
  PRIMARY KEY (`id`),
  UNIQUE KEY `contact_id` (`contact_id`),
  KEY `email` (`email`),
  KEY `add_time` (`add_time`),
  KEY `update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Bảng lưu thông tin chi tiết mail nhắc gia hạn MSC';

CREATE TABLE IF NOT EXISTS `nv4_vi_active_profile_dtnet_mkt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contact_id` varchar(50) NOT NULL DEFAULT '' COMMENT 'Mã số thuế',
  `email` varchar(250) NOT NULL DEFAULT '' COMMENT 'Email',
  `contact_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'Loại mail kích hoạt hồ sơ: 1 hoặc 2 tùy thuộc vào việc đã đăng ký tài khoản hay chưa',
  `company_name` varchar(250) NOT NULL DEFAULT '' COMMENT 'Tên doanh nghiệp',
  `link_bids_profile` text  DEFAULT '' COMMENT 'Link tên doanh nghiệp bên dauthau.net',
  `rep_name` varchar(250) NOT NULL DEFAULT '' COMMENT 'Tên người đại diện',
  `add_time` int(11) NOT NULL DEFAULT 0,
  `updatetime` int(11) NOT NULL DEFAULT 0,
  `verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Đánh dấu doanh nghiệp đã xác thực trên dauthau.net',
  PRIMARY KEY (`id`),
  UNIQUE KEY `contact_id` (`contact_id`),
  KEY `email` (`email`),
  KEY `add_time` (`add_time`),
  KEY `updatetime` (updatetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Bảng lưu thông tin chi tiết mail kích hoạt hồ sơ dauthau.net';

- Chạy tool create_mail/active_profile_dtnet.sh và create_mail/new_msc_renewal.sh để lưu thông tin vào bảng marketing (chạy tool này mỗi ngày 1 lần)

# 7/05/2025 by tuyenhv:
ALTER TABLE `nv4_vi_bidding_bidkqlc_id` ADD INDEX(`send_status`, `userid`);
ALTER TABLE `nv4_vi_bidding_bidkqlc_id` DROP INDEX `userid`;
ALTER TABLE `nv4_vi_bidding_bidkqlc_id` DROP INDEX `filter_id`;
ALTER TABLE `nv4_vi_bidding_bidkqlc_id` DROP INDEX `addtime`;

# 23/04/2025 by hoangvi:
Chạy tool sau 1 lần để cập nhật các trường trong bảng _bidding_detail lên ES:
tools/elastic_update_bidding_field.sh

# 08/04/2025 by Huy:
ALTER TABLE `nv4_vi_bidding_bid_id`
ADD `clarify_id` VARCHAR(255) DEFAULT '' COMMENT 'ID của thông báo yêu cầu bổ sung thông tin' AFTER `bid_id`,
ADD `petition_row_id` INT(11) NOT NULL DEFAULT 0 COMMENT 'ID của thông báo yêu cầu kiến nghị' AFTER `clarify_id`,
CHANGE `reasonid` `reasonid` TINYINT(1) NULL DEFAULT '0' COMMENT '0: Không có hủy thầu, 1: có hủy thầu',
ADD INDEX `reasonid` (`reasonid`),
ADD INDEX `clarify_id` (`clarify_id`),
ADD INDEX `petition_row_id` (`petition_row_id`),
ADD INDEX `notify_vip` (`notify_vip`);

ALTER TABLE `nv4_en_bidding_bid_id`
ADD `clarify_id` VARCHAR(255) DEFAULT '' COMMENT 'ID của thông báo yêu cầu bổ sung thông tin' AFTER `bid_id`,
ADD `petition_row_id` INT(11) NOT NULL DEFAULT 0 COMMENT 'ID của thông báo yêu cầu kiến nghị' AFTER `clarify_id`,
CHANGE `reasonid` `reasonid` TINYINT(1) NULL DEFAULT '0' COMMENT '0: Không có hủy thầu, 1: có hủy thầu',
ADD INDEX `reasonid` (`reasonid`),
ADD INDEX `clarify_id` (`clarify_id`),
ADD INDEX `petition_row_id` (`petition_row_id`),
ADD INDEX `notify_vip` (`notify_vip`);

Cấu hình config define('URL_RE', 'https://url.dauthau.asia/');

# 08/03/2025 by DatHQ: Bổ sung cấu hình (includes/config.php)
define('DAUGIA_DOMAIN', 'https://daugia.net');

# 5/03/2025 by Tuyenhv: fix lỗi lọc tin thầu,
nguyên nhân do ghi đè  $search_elastic['must'] dẫn tới các đoạn  $search_elastic['must'] ở trên giới hạn theo id bị mất.

Chạy lại gửi email như sau:
	SELECT * FROM `nv4_vi_bidding_logs` WHERE from_time > 1741138200;
lấy from_id nhỏ nhất, sau đó xóa toàn bộ dữ liệu cũ
	DELETE FROM nv4_vi_bidding_bid_id WHERE send_status = 0 AND bid_id < from_id;
rồi update để chạy lại
	UPDATE `nv4_vi_bidding_logs` SET `status` = '0' WHERE from_time > 1741138200;

làm tương tự với pro1
	SELECT * FROM `nv4_vi_bidding_logs_pro` WHERE from_time > 1741138200;
lấy from_id nhỏ nhất, sau đó xóa toàn bộ dữ liệu cũ
	DELETE FROM nv4_vi_bidding_pro1_id WHERE send_status = 0 AND bid_id < from_id;
rồi update để chạy lại
	UPDATE `nv4_vi_bidding_logs_pro` SET `status` = '0' WHERE from_time > 1741138200;

# 20/02/2025 by DatHQ: (lệnh SQL nếu chạy ở kho chính thì không cần chạy lại)
-- Chạy file sau elastic_mappings_dg_legal.php
ALTER TABLE `nv4_vi_van_ban_dau_gia_row` ADD `elasticsearch` INT NOT NULL DEFAULT '0' AFTER `status`, ADD INDEX (`elasticsearch`);

# 17/02/2025 by Đan
Chạy tool sau 1 lần:
tools_2025/update_content_tbmt.sh

# 12/02/2025 by Đan
Xóa các file
rm -f tools/elastic_update_field_*.txt
Chạy 1 lần tool sau: tools/elastic_update_field.sh

# 10/01/2025 by Đạt:
- Chạy 1 lần duy nhất file tools/fix_bidfieid_data.sh

# 26/12/2024 by Đan
Set tool sau 1 phút 1 lần
create_mail/plp_report.php

# 20/12/2024 by Đan
Chạy 2 tool sau 1 lần:
tools/update_result_capital.php
tools/update_result_capital.php --site_lang=en

# 20/12/2024 by Huy:
ALTER TABLE `nv4_bidding_filter` ADD `type_kqlcnt` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0 là Tất cả, 1 là có liên kết vs KH, 2 là không liên kết vs KH' AFTER `phanmuc`

# 14/12/2024 by Đan
Chạy tool sau 1 lần: tools/elastic_update_field.php, tools/elastic_update_field.php --sitelang=en

# 02/12/2024 by Lâm:
Thực hiện chạy tool 1 tại thư mục tools:
bash update_bid_id.sh

# 28/11/2024 by Huy:
Chạy câu lệnh này bên site info
ALTER TABLE `nv4_vi_bidding_row` ADD `type_choose_id` TINYINT(2) NOT NULL DEFAULT 0 COMMENT 'Hình thức lựa chọn ID' AFTER `hinh_thuc_lua_chon`;
ALTER TABLE `nv4_en_bidding_row` ADD `type_choose_id` TINYINT(2) NOT NULL DEFAULT 0 COMMENT 'Hình thức lựa chọn ID' AFTER `hinh_thuc_lua_chon`;
ALTER TABLE `nv4_vi_bidding_row` ADD INDEX type_choose_id (type_choose_id);
ALTER TABLE `nv4_en_bidding_row` ADD INDEX type_choose_id (type_choose_id);

Chạy tool này 1 lần trc tools/update_type_choose_id.sh để cập nhật type_choose_id cho tbmt và đưa lên ES

# 28/11/2024 by Thảo
ALTER TABLE `nv4_bidding_result_goods` CHANGE `manufacturer` `manufacturer` VARCHAR(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Hãng sản xuất';

# 25/11/2024 by Lâm
ALTER TABLE `nv4_bidding_result_goods` ADD `manufacturer` VARCHAR(500) NOT NULL DEFAULT '' COMMENT 'Hãng sản xuất' AFTER `lable_good`;

Thực hiện truy cập vào thư mục tools:
1. xoá file update_goods_2024.txt
2. bash update_goods_2024.sh

# 20/11/2024 by Tuyenhv: bổ sung trigger khi xóa bảng row

CREATE TRIGGER `nv4_vi_bidding_row_delete` BEFORE DELETE ON `nv4_vi_bidding_row`
 FOR EACH ROW BEGIN
	INSERT INTO `nv4_vi_bidding_row_trigger`(`id`, `trigger_time`) VALUES (OLD.id, UNIX_TIMESTAMP());
END

chạy thêm các đoạn sql trong update_search.php phần xóa các thừa trên ES do xóa bảng nv4_vi_bidding_row thủ công
theo các id tbmt đã xóa bên dưới của lâm xóa

# 18/11/2024 by Lâm:
Do dữ liệu thông báo mời thầu của msc cũ do lúc trước có 1 số dữ liệu bị bóc lặp lại 2 lần nên cần xoá dữ liệu trên mysql và elastic để cho dữ liệu đúng đắn về tính dữ liệu

POST /dauthau_bidding/_delete_by_query
{
  "query": {
    "terms": {
      "id": [
        872646, 870065, 873435, 870039, 873505,
        873285, 873051, 873490, 873584, 873698,
        875482, 973787, 973730, 972297, 972011,
        971890, 971852, 972443, 973811, 973623,
        973703, 973597, 973602, 973652, 973779,
        977051
      ]
    }
  }
}

POST /en_dauthau_bidding/_delete_by_query
{
  "query": {
    "terms": {
      "id": [
        872646, 870065, 873435, 870039, 873505,
        873285, 873051, 873490, 873584, 873698,
        875482, 973787, 973730, 972297, 972011,
        971890, 971852, 972443, 973811, 973623,
        973703, 973597, 973602, 973652, 973779,
        977051
      ]
    }
  }
}

DELETE FROM `nv4_vi_bidding_row` WHERE `id` IN (872646,870065,873435,870039,873505,873285,873051,873490,873584,873698,875482,973787,973730,972297,972011,971890,971852,972443,973811,973623,973703,973597,973602,973652,973779,977051);
DELETE FROM `nv4_en_bidding_row` WHERE `id` IN (872646,870065,873435,870039,873505,873285,873051,873490,873584,873698,875482,973787,973730,972297,972011,971890,971852,972443,973811,973623,973703,973597,973602,973652,973779,977051);

# 18/11/2024 by Đạt:
- Chạy 1 lần duy nhất file tools/fix_violate_tbmt_2022.php
- Chạy 1 lần duy nhất file tools/fix_violate_khlcnt_2022.php

# 11/11/2024 by Huy:
ALTER TABLE `nv4_vi_bidding_result` ADD `type_kqlcnt` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '0: có KHLCNT, 1: không có KHLCNT(mới), 2: không có KHLCNT(cũ)';
ALTER TABLE `nv4_en_bidding_result` ADD `type_kqlcnt` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '0: có KHLCNT, 1: không có KHLCNT(mới), 2: không có KHLCNT(cũ)';
ALTER TABLE `nv4_vi_bidding_result` ADD INDEX `type_kqlcnt` (`type_kqlcnt`);
ALTER TABLE `nv4_en_bidding_result` ADD INDEX `type_kqlcnt` (`type_kqlcnt`);

+ Truy cập vào index dauthau_result -> vào phần Any Request thiết lập:
PUT /dauthau_result/_mapping
{
  "properties": {
    "type_kqlcnt": {
      "type": "long"
    }
  }
}

POST /dauthau_result/_update_by_query
{
  "script": {
    "source": "if (ctx._source.type_kqlcnt == null) { ctx._source.type_kqlcnt = 0; }",
    "lang": "painless"
  }
}

PUT /en_dauthau_result/_mapping
{
  "properties": {
    "type_kqlcnt": {
      "type": "long"
    }
  }
}

POST /en_dauthau_result/_update_by_query
{
  "script": {
    "source": "if (ctx._source.type_kqlcnt == null) { ctx._source.type_kqlcnt = 0; }",
    "lang": "painless"
  }
}

# 28/10/2024 by Lâm:
Thêm câu lệnh này bên site info
ALTER TABLE `nv4_bidding_result_goods` ADD `lable_good` VARCHAR(2000) NOT NULL DEFAULT '' COMMENT 'Nhãn hiệu';

+ Thực hiện xoá file trong thư mục tools: update_goods_2024.txt
+ Thực hiện chạy tool 1 lần trong thư mục tools cho đến hết: bash update_goods_2024.sh

# 25/10/2024 by Đạt:
- Chạy 1 lần duy nhất file tools/fix_violate_tbmt.php
- Chạy 1 lần duy nhất file tools/fix_violate_khlcnt.php

# 16/10/2024 by Lâm:
Thực hiện chạy tool 1 lần trong thư mục tools: bash update_goods_2024.sh

# 28/09/2024 by Ngân:

Chạy file sau elastic_mappings_dt_legal.php

ALTER TABLE `nv4_vi_van_ban_dau_thau_row` ADD `elasticsearch` INT NOT NULL DEFAULT '0' AFTER `status`, ADD INDEX (`elasticsearch`);
ALTER TABLE `nv4_en_van_ban_dau_thau_row` ADD `elasticsearch` INT NOT NULL DEFAULT '0' AFTER `status`, ADD INDEX (`elasticsearch`);

# 21/09/2024 by Đạt:
- Chạy 1 lần duy nhất file tools/check_violate_khlcnt.php và tools/check_violate_tbmt.php

# 12/06/2024 by Đạt:
- Chạy 1 lần duy nhất file tools/fix_phuong_thuc_num_sql.php

# 05/06/2024 by Tùng: Chuyển API SetCustomEmailDead sang site api
// Cấu hình API của site api:
define('API_API_URL', 'https://api.dauthau.asia/api.php');
define('API_API_KEY', '');
define('API_API_SECRET', '');

# 04/06/2024 by Lâm:
Thêm dữ liệu bên site info
ALTER TABLE `nv4_vi_bidding_plans` ADD `list_id_province` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Danh sách các tỉnh thành của gói thầu bên bảng plan_contract' AFTER `location`, ADD INDEX (`list_id_province`);
ALTER TABLE `nv4_en_bidding_plans` ADD `list_id_province` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Danh sách các tỉnh thành của gói thầu bên bảng plan_contract' AFTER `location`, ADD INDEX (`list_id_province`);

Chạy tool 1 lần tại thư mục tools để update lại danh sách tỉnh thành:
+ bash update_province_plans.sh

# 22/05/2024 by Đạt:
- Chạy 1 lần duy nhất file tools/copy_phuong_thuc_tbmt.php

# 22/05/2024 by tuyenhv: thêm ghi log khlcnt aita
ALTER TABLE `nv4_vi_aita_plan_id` ADD `error_info` TEXT NOT NULL DEFAULT '' AFTER `send_status`;

# 02/05/2024 by Ngân: Thực hiện lần lượt các bước sau
- Chạy 1 lần duy nhất file tools/update_data_solicitor_unlink để gộp những bên mời thầu không liên kết được với bên mời thầu
 vào danh sách bên mời thầu không tìm thấy thông tin

# 15/04/2024 by Đạt: chuyển cấu hình log lỗi về channel dauthau-notification

# 06/03/2024 by Lâm:
-- SQL bên site info
ALTER TABLE `nv4_vi_bidding_result_project` ADD `is_msc_new` INT NOT NULL DEFAULT '0' COMMENT '0: MSC cũ\r\n1: MSC mới';
ALTER TABLE `nv4_en_bidding_result_project` ADD `is_msc_new` INT NOT NULL DEFAULT '0' COMMENT '0: MSC cũ\r\n1: MSC mới';

Thực hiện chạy trong thư mục tools để cập nhật nhật trường is_new_msc của bảng  nv4_vi_bidding_result lên trên Elastic:
php update_kqlcnt_msc_cu.php

# 12/03/2024 by Phụng: Thêm cột province_id trên elastic
UPDATE nv4_vi_project_investment SET elasticsearch = 9;
UPDATE nv4_en_project_investment SET elasticsearch = 9;

# 24/02/2024 by Đan:
Chạy lần lượt 3 tool sau:
tools/elastic_dauthau_result_reindex.php
tools/elastic_dauthau_result_reindex.php --site_lang=en
tools/result_goods_content.php
Ép chạy tool update_search_result.sh để đẩy lại dữ liệu lên es

# 30/01/2024 by Đạt: chạy tool gửi mail thông báo sắp tới hạn nộp hồ sơ dự thầu
- chạy /create_mail/remind_online.php 5 phút 1 lần

# 23/01/2024 BY TRUNG:
 Thêm key vào file config: define('UNSUBCRIBE_KEY', 'BSzfSNn4DoT1GHgFqoFHb7PCEQzRVSvH');

# 17/01/2024 by Tuyenhv: Thêm bảng log event mail gửi đi
CREATE TABLE IF NOT EXISTS `nv4_bidding_mail_event` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL DEFAULT '',
  `email` varchar(250) NOT NULL DEFAULT '',
  `messageid` varchar(250) NOT NULL DEFAULT '',
  `desciption` text NOT NULL DEFAULT '',
  `addtime` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_2` (`type`,`email`,`messageid`),
  KEY `type` (`type`),
  KEY `email` (`email`),
  KEY `messageid` (`messageid`),
  KEY `addtime` (`addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

# 29/12/2023 by Đạt: chạy tool gửi mail thông báo sắp tới hạn nộp hồ sơ dự thầu
- chạy /create_mail/remind.php 5 phút 1 lần

# 25/12/2023 by Tùng: thêm comment cho trường send_status
ALTER TABLE `nv4_vi_bidding_bid_id` CHANGE `send_status` `send_status` INT(11) NOT NULL DEFAULT '0' COMMENT '-100: Chỉ xuất excel, không gửi\r\n0: Chưa gửi\r\n>1: Đã gửi';
ALTER TABLE `nv4_vi_bidding_plan_id` CHANGE `send_status` `send_status` INT(11) NOT NULL COMMENT '-100: Chỉ xuất excel, không gửi\r\n0: Chưa gửi\r\n1: Đã gửi';

# 29/11/2023 by Lâm: Thêm CSDL và DB dauthau.info
ALTER TABLE `nv4_vi_bidding_project_proposal` ADD `alias` VARCHAR(220) NOT NULL DEFAULT '' AFTER `ten_du_an`;
ALTER TABLE `nv4_en_bidding_project_proposal` ADD `alias` VARCHAR(220) NOT NULL DEFAULT '' AFTER `ten_du_an`;

ALTER TABLE `nv4_vi_bidding_plans_project` ADD `alias` VARCHAR(220) NOT NULL DEFAULT '' AFTER `title`;
ALTER TABLE `nv4_en_bidding_plans_project` ADD `alias` VARCHAR(220) NOT NULL DEFAULT '' AFTER `title`;

Chạy tool sau để đẩy alias của KHLCNDT và Dự án mới được công bố
tools/update_alias_project_proposal.php
tools/update_alias_project_proposal.php --site_lang=en

tools/update_alias_plans_project.php
tools/update_alias_plans_project.php --site_lang=en

# 27/11/2023 by Thảo:
ALTER TABLE `nv4_vi_bidding_plan_id` ADD INDEX(`send_status`);

# 06/11/2023 by Ngân:
Chạy các tool sau 1 lần duy nhất để cập nhật lại dữ liệu content_goods
  - tools/update_content_goods.php
  - tools/update_content_goods.php --site_lang=en

# 31/10/2023 by Lâm:
Thực hiện tạo thư mục nằm ở thư mục gốc có tên là: data_inform

# 27/10/2023 by Đan: Tạo bảng lưu danh sách hàng hóa ở api tìm kiếm tbmt do hàng hóa phân lô TBMT chưa có chỗ lấy
CREATE TABLE `nv4_bidding_goods_tmp` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `id_msc` VARCHAR(50) NOT NULL DEFAULT 0 COMMENT 'ID của TBMT trên msc',
 `good_name` varchar(2000) NOT NULL DEFAULT '',
 PRIMARY KEY (`id`),
 INDEX `id_msc` (`id_msc`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT "Bảng lưu tạm danh sách hàng hóa lúc bóc tin do chưa có chỗ lấy danh sách HH phân lô";

# 24/10/2023 by Tuyen: chạy lại dữ liệu đấu giá lên es từ ngày 12/10
UPDATE `nv4_dau_gia_bid` SET `elasticsearch` = '0' WHERE `date_bid` >= 1697043600;
UPDATE `nv4_dau_gia_bid_select` SET `elasticsearch` = '0' WHERE `opening_bid` >= 1697043600;

# 13/10/2023 by Lâm:
Xoá index bidding_goods đi cho đẩy lại toàn bộ bidding_goods để cập nhật so_tbmt và ngay_dang_tai

# 17/10/2023 by Đan: Cập nhật lại một số TBMT lên ES
UPDATE nv4_vi_bidding_row SET elasticsearch = 9 WHERE id IN (SELECT DISTINCT id_detail FROM nv4_bidding_detail_subdivision);
UPDATE nv4_en_bidding_row SET elasticsearch = 9 WHERE id IN (SELECT DISTINCT id_detail FROM nv4_bidding_detail_subdivision);

# 13/10/2023 by Đan
ALTER TABLE `nv4_vi_bidding_row` ADD `content_goods` MEDIUMTEXT NOT NULL DEFAULT '' AFTER `content_full`;
ALTER TABLE `nv4_en_bidding_row` ADD `content_goods` MEDIUMTEXT NOT NULL DEFAULT '' AFTER `content_full`;
### Chạy tool 1 lần: `tools/tbmt_goods_content.php` và `tools/tbmt_goods_content.php --site_lang=en`

# 12/10/2023 by tuyenhv: cho chạy lại tin đấu giá từ ngày 03/10
- UPDATE `nv4_vi_bidding_dau_gia_logs` SET `userid`=0,`status`=0 WHERE from_time >=1696266000;
- chạy sendmail-aws.my/filter_mail/daugia.php theo số lượng log ở trên đã update
- chạy sendmail-aws.my/create_mail/dau_gia.php theo số lượng nv4_vi_bidding_dau_gia_id send_status = 0

# 29/09/2023 by Thao: cập nhật alias cho bidding_result
ALTER TABLE `nv4_vi_bidding_result` ADD `alias` VARCHAR(220) NOT NULL DEFAULT '' AFTER `title`;
ALTER TABLE `nv4_en_bidding_result` ADD `alias` VARCHAR(220) NOT NULL DEFAULT '' AFTER `title`;

Chạy tool sau để đẩy alias của dự án phát triển lên ES
tools/update_alias_result.php
tools/update_alias_result.php --site_lang=en

# 10/10/2023 by Ngân
Chạy tool sau để đẩy alias của dự án phát triển site tiếng anh lên ES
tools/devprojects_alias_to_es.php --site_lang=en

#05/10/2023 Ton: Sửa CSDL

ALTER TABLE `nv4_vi_bidding_detail` ADD `sync_drive` INT(11) NOT NULL DEFAULT '0';
ALTER TABLE `nv4_en_bidding_detail` ADD `sync_drive` INT(11) NOT NULL DEFAULT '0';

DROP TABLE IF EXISTS `nv4_bidding_drive_tbmt`;
CREATE TABLE `nv4_bidding_drive_tbmt` (
  `drive_id` int(11) NOT NULL AUTO_INCREMENT,
  `bid_id` int(11) NOT NULL DEFAULT 0,
  `title` varchar(255) NOT NULL DEFAULT '',
  `url` varchar(255) NOT NULL DEFAULT '',
  `drive` varchar(255) NOT NULL DEFAULT '',
  `filesize` int(11) NOT NULL DEFAULT 0,
  `id_file_msc` varchar(37) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1: file hồ sơ mời thầu\r\n2: file quyết định',
  `note` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`drive_id`,`bid_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

PARTITION BY RANGE (`bid_id`)
(
    PARTITION p01 VALUES LESS THAN (50000) ENGINE=InnoDB,
    PARTITION p02 VALUES LESS THAN (100000) ENGINE=InnoDB,
    PARTITION p03 VALUES LESS THAN (150000) ENGINE=InnoDB,
    PARTITION p04 VALUES LESS THAN (200000) ENGINE=InnoDB,
    PARTITION p05 VALUES LESS THAN (250000) ENGINE=InnoDB,
    PARTITION p06 VALUES LESS THAN (300000) ENGINE=InnoDB,
    PARTITION p07 VALUES LESS THAN (350000) ENGINE=InnoDB,
    PARTITION p08 VALUES LESS THAN (400000) ENGINE=InnoDB,
    PARTITION p09 VALUES LESS THAN (450000) ENGINE=InnoDB,
    PARTITION p10 VALUES LESS THAN (500000) ENGINE=InnoDB,
    PARTITION p11 VALUES LESS THAN (550000) ENGINE=InnoDB,
    PARTITION p12 VALUES LESS THAN (600000) ENGINE=InnoDB,
    PARTITION p13 VALUES LESS THAN (650000) ENGINE=InnoDB,
    PARTITION p14 VALUES LESS THAN (700000) ENGINE=InnoDB,
    PARTITION p15 VALUES LESS THAN (750000) ENGINE=InnoDB,
    PARTITION p16 VALUES LESS THAN (800000) ENGINE=InnoDB,
    PARTITION p17 VALUES LESS THAN (850000) ENGINE=InnoDB,
    PARTITION p18 VALUES LESS THAN (900000) ENGINE=InnoDB,
    PARTITION p19 VALUES LESS THAN (950000) ENGINE=InnoDB,
    PARTITION p20 VALUES LESS THAN (1000000) ENGINE=InnoDB,
    PARTITION p21 VALUES LESS THAN (1050000) ENGINE=InnoDB,
    PARTITION p22 VALUES LESS THAN (1100000) ENGINE=InnoDB,
    PARTITION p23 VALUES LESS THAN (1150000) ENGINE=InnoDB,
    PARTITION p24 VALUES LESS THAN (1200000) ENGINE=InnoDB,
    PARTITION p25 VALUES LESS THAN (1250000) ENGINE=InnoDB,
    PARTITION p26 VALUES LESS THAN (1300000) ENGINE=InnoDB,
    PARTITION p27 VALUES LESS THAN (1350000) ENGINE=InnoDB,
    PARTITION p28 VALUES LESS THAN (1400000) ENGINE=InnoDB,
    PARTITION p29 VALUES LESS THAN (1450000) ENGINE=InnoDB,
    PARTITION p99 VALUES LESS THAN MAXVALUE ENGINE=InnoDB
);

# 29/09/2023 by Thao
ALTER TABLE `nv4_vi_bidding_open` ADD `alias` VARCHAR(220) NOT NULL DEFAULT '' AFTER `goi_thau`;
ALTER TABLE `nv4_en_bidding_open` ADD `alias` VARCHAR(220) NOT NULL DEFAULT '' AFTER `goi_thau`;
Chạy tool sau để đẩy alias của dự án phát triển lên ES
tools/update_alias_open.php
tools/update_alias_open.php --site_lang=en

# 27/09/2023 by Đan: chạy tool 1 lần để xóa các KHLCNT bị insert trùng
tools/remove_duplicate_plans.php

# 27/09/2023 by Đan
Chạy tool sau để đẩy alias của dự án phát triển lên ES
tools/devprojects_alias_to_es.php
tools/devprojects_alias_to_es.php --site_lang=en
// Log các row bị lỗi khi đẩy nếu có sẽ nằm ở tools/devprojects_alias_to_es_error.log

# 18/09/2023 by Đan: chạy tool sau 1 lần:
tools/fix_tbmt_en_alias.php

# 30/08/2023 by Ton: Chạy 2 file, chỉ cần chạy nếu có làm liên quan đến Aita
- tools/update_tbmt2_aita.sh  //ĐẨY LẠI PHẦN PHÂN LÔ
- tools/update_result_aita.sh //ĐẨY LẠI PHẦN PHÂN LÔ + ID_MSC

# 05/08/2023 by Đan
Đẩy lại các dữ liệu để thêm trường tìm kiếm theo tên không dấu
UPDATE nv4_vi_bidding_row SET elasticsearch = 0;
UPDATE nv4_en_bidding_row SET elasticsearch = 0;
UPDATE nv4_vi_bidding_plans SET elasticsearch = 0;
UPDATE nv4_en_bidding_plans SET elasticsearch = 0;
UPDATE nv4_vi_bidding_open SET elasticsearch = 0;
UPDATE nv4_en_bidding_open SET elasticsearch = 0;
UPDATE nv4_vi_bidding_result SET elasticsearch = 0;
UPDATE nv4_en_bidding_result SET elasticsearch = 0;
UPDATE nv4_vi_project_investment SET elasticsearch = 0;
UPDATE nv4_en_project_investment SET elasticsearch = 0;
UPDATE nv4_dau_gia_bid SET elasticsearch = 0;
UPDATE nv4_dau_gia_bid_select SET elasticsearch = 0;
Chạy tool /update_search.sh để đẩy dữ liệu

# 05/08/2023 by Ton: Chạy 4 file, chỉ cần chạy nếu có làm liên quan đến Aita
- tools/update_project_aita.sh
- tools/update_khlcnt_aita.sh
- tools/update_tbmt_aita.sh
- tools/update_result_aita.sh

UPDATE `nv4_vi_aita_devproject_id` SET `send_status`=0 WHERE `send_status` < 0;
UPDATE `nv4_vi_aita_plan_id` SET `send_status`=0 WHERE `send_status` < 0;
UPDATE `nv4_vi_aita_bid_id` SET `send_status`=0, error_info='' WHERE `send_status` < 0;
UPDATE `nv4_vi_aita_result_id` SET `send_status`=0, error_info='' WHERE `send_status` < 0;

Chạy bash /aita/push_data.sh để đẩ dữ liệu qua aita

# 02/08/2023 by Dũng: Thêm config

Bổ sung vào includes/config.php config như sau, lấy từ site ID cho nhanh
// Cấu hình API của dauthau.info
define('API_DAUTHAUINFO_URL', 'https://dauthau.asia/api.php');
define('API_DAUTHAUINFO_KEY', '....');
define('API_DAUTHAUINFO_SECRET', '....');


# 07/07/2023 by Lâm:
// API ID.dauthau.net
Cấu hình api cho role DauThau User Role của site id vào file config.php
define('ID_API_URL', 'https://id.dauthau.net/api.php');
define('ID_API_USER_KEY', '');
define('ID_API_USER_SECRET', '');


# 29/06/2023 by Tùng: Chạy lại tool để cập nhật tên tiếng anh vào array_profile bảng nv4_businesslistings_info
- Chạy sql:
UPDATE `nv4_businesslistings_info` SET `profile_id` = '0'

- Chạy lại tool update_tab_contractor_dauthau.net.php cho đến khi hết dữ liệu

# 16/06/2023 by Tuyên: đẩy lại KQLCNT lên ES do bổ sung thêm trường post_time và date_approval
UPDATE nv4_vi_bidding_result SET elasticsearch = 0 WHERE elasticsearch = 6;
UPDATE nv4_en_bidding_result SET elasticsearch = 0 WHERE elasticsearch = 6;

# 26/05/2023
thêm vào config.php
define('NV_API_SLACK', '*******************************************************************************');
define('NV_CHANNEL_SLACK', 'dauthau-kythuat');

# 08/05/2023 by Đan: Đẩy lại các dữ liệu result bị lỗi
UPDATE nv4_vi_bidding_result SET elasticsearch = 0 WHERE elasticsearch = 11;


# 08/03/2023 by Đan: Đẩy lại dữ liệu result lên ES:
UPDATE nv4_vi_bidding_result SET elasticsearch = 0;
Chạy file /update_search_result.sh đến hết

# 06/03/2023 by Tùng: Thêm trường investor_id vào elastic của dauthau_bidding
UPDATE `nv4_vi_bidding_row` SET `elasticsearch` = '0';

- Chạy tool update_search.php cho đến khi hết dữ liệu dauthau_bidding

## Kho sendmail-aws
- Chạy tool create_mail\active_profile_dtnet.php để gửi mail Xác thực hồ sơ trên dauthau.net (insert vào bảng nv4_vi_bidding_mail) (chạy tool này mỗi ngày 1 lần)

# 02/03/2023 by Thao: Thêm cấu hình elas_result
INSERT INTO `nv4_config` (`lang`, `module`, `config_name`, `config_value`) VALUES
('vi', 'bidding', 'elas_result_host', 'elastic_dtnet'),
('vi', 'bidding', 'elas_result_pass', ''),
('vi', 'bidding', 'elas_result_port', '9200'),
('vi', 'bidding', 'elas_result_use', '1'),
('vi', 'bidding', 'elas_result_user', 'elastic');

# 03/03/2023 by Đan: Đẩy lại dữ liệu result lên ES:
UPDATE nv4_vi_bidding_result SET elasticsearch = 0;
UPDATE nv4_vi_bidding_result_goods SET elasticsearch = 0;
Chạy file /update_search_result.sh đến hết

# 02/03/2023 by Thao: Thêm cấu hình
INSERT INTO `nv4_config` (`lang`, `module`, `config_name`, `config_value`) VALUES
('vi', 'bidding', 'elas_mail_host', 'elastic_dtnet'),
('vi', 'bidding', 'elas_mail_pass', ''),
('vi', 'bidding', 'elas_mail_port', '9200'),
('vi', 'bidding', 'elas_mail_use', '1'),
('vi', 'bidding', 'elas_mail_user', 'elastic');

# 7/02/2023: by Thảo: Thêm thứ tự ưu tiên khi gửi mail
ALTER TABLE `nv4_vi_bidding_mail` ADD `priority` INT NOT NULL DEFAULT '1' COMMENT 'Độ ưu tiên của Email' AFTER `status`;

# 7/02/2023: by Thảo: Sửa lại UNIQUE bidding_mail
ALTER TABLE `nv4_vi_bidding_mail` DROP INDEX `main_mail`;
ALTER TABLE `nv4_vi_bidding_mail` ADD UNIQUE(`main_mail`, `title`, `addtime`);

# 13/01/2023: by Phụng:
//Chạy file \sendmail-aws\tools\update_content_notify_no_KQLCNT.php để cập nhật notify_no trong content KQLCNT
//Lưu ý: Mỗi lần em chỉ cho chạy 100 row
//Chạy file \sendmail-aws\tools\update_content_notify_no_KQMT.php để cập nhật notify_no trong content KQMT
//Lưu ý: Mỗi lần em chỉ cho chạy 100 row
//Chạy file \sendmail-aws\tools\update_content_notify_no_TBMT.php để cập nhật notify_no trong content TBMT
//Lưu ý: Mỗi lần em chỉ cho chạy 100 row

# 13/01/2023: by Phụng:
//Chạy file \sendmail-aws\tools\update_content_khlcnt_code_TBMT.php để cập nhật khlcnt_code trong content TBMT
//Lưu ý: Mỗi lần em chỉ cho chạy 100 row

# 10/01/2023: by Tùng:
Cấu hình vào config.php

// Cấu hình API  của id.dauthau.net
define('API_CRM_URL', 'https://id.dauthau.net/api.php');
define('API_CRM_KEY', '');
define('API_CRM_SECRET', '');

// API hồ sơ dkkd trên dauthau.net
define('API_DAUTHAUNET_URL', 'https://dauthau.net/api.php');
define('API_DAUTHAUNET_KEY', '');
define('API_DAUTHAUNET_SECRET', '');

// Cấp quyền ListUser, GetBidsProfile trong module users
// Cấp quyền GetBidsProfile, GetProfilePermisson của hệ thống

# 25/11/2022: by Tùng: cập nhật lại content và content_full cho dữ liệu nhà thầu (thêm orgcode)
UPDATE `nv4_vi_businesslistings_info` SET `elasticsearch` = '0' WHERE orgcode != '';

#27/10/2022: by Tuyenhv: ghi lại lôi khi push
ALTER TABLE `nv4_vi_aita_bid_id` ADD `error_info` TEXT NOT NULL DEFAULT '' AFTER `send_status`;
ALTER TABLE `nv4_vi_aita_result_id` ADD `error_info` TEXT NOT NULL DEFAULT '' AFTER `send_status`;

#20/10/2022: by Tuyenhv: Thêm dữ liệu phục vụ site aita
ALTER TABLE `nv4_vi_bidding_plans` ADD `content_aita` TEXT NOT NULL DEFAULT '' AFTER `content_full`;
ALTER TABLE `nv4_vi_aita_plan_id` CHANGE `location` `location` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Vị trí file insert dòng dữ liệu này';
ALTER TABLE `nv4_vi_aita_result_id` CHANGE `location` `location` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Vị trí file insert dòng dữ liệu này';
ALTER TABLE `nv4_vi_aita_bid_id` CHANGE `location` `location` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Vị trí file insert dòng dữ liệu này';

#20/10/2022: by Tuyenhv: thêm trường đánh dấu
ALTER TABLE `nv4_vi_bidding_plans_contract` ADD `is_aita` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '0: không thuộc cntt\r\n1: thuộc cntt';

#18/10/2022: by Tuyenhv: thêm trường đánh dấu đã đồng bộ sang aita
ALTER TABLE `nv4_vi_bidding_solicitor` ADD `update_aita` INT NOT NULL DEFAULT '0' AFTER `time_hide_end`;


#07/07/2022: Thao cập nhật CSDL lưu log
    ALTER TABLE `nv4_vi_aita_devproject_logs` ADD `new_devproject_id` INT NOT NULL DEFAULT '0' AFTER `status`;
    ALTER TABLE `nv4_vi_aita_plan_logs` ADD `new_plan_id` INT NOT NULL DEFAULT '0' AFTER `send_status`;
    ALTER TABLE `nv4_vi_aita_bid_logs` ADD `new_bid_id` INT NOT NULL DEFAULT '0' AFTER `status`;

#27/06/2022: Tôn cập nhật CSDL
    ALTER TABLE `nv4_vi_aita_devproject_id` ADD `location` VARCHAR(30) NOT NULL DEFAULT '' COMMENT 'Vị trí file insert dòng dữ liệu này' AFTER `devproject_id`;
    ALTER TABLE `nv4_vi_aita_plan_id` ADD `location` VARCHAR(30) NOT NULL DEFAULT '' COMMENT 'Vị trí file insert dòng dữ liệu này' AFTER `plan_id`;
    ALTER TABLE `nv4_vi_aita_bid_id` ADD `location` VARCHAR(30) NOT NULL DEFAULT '' COMMENT 'Vị trí file insert dòng dữ liệu này' AFTER `bid_id`;
    ALTER TABLE `nv4_vi_aita_result_id` ADD `location` VARCHAR(30) NOT NULL DEFAULT '' COMMENT 'Vị trí file insert dòng dữ liệu này' AFTER `result_id`;

#25/06/2022: Tôn cập nhật CSDL, xoá trường is_check do không cần dùng đến
    ALTER TABLE `nv4_vi_aita_result_id` DROP `is_check`;

#22/06/2022: Tuyên: Cập nhật config aita + CSDL
    #Thêm 2 biến vào cầu hình config_aita.php
    $check_time_aita = 1640970000; // Ngày 1/1/2022
    ALTER TABLE `nv4_vi_aita_plan_id` ADD `keyword` TEXT NOT NULL DEFAULT '' COMMENT 'keyword của filter' AFTER `plan_id`;

#10/06/2022: Tuyên: đánh dấu trạng thái chuyển dữ liệu is_aita
Quy định:
is_aita = 0: chưa xác định
is_aita = 1: lọc qua từ khóa, chuyển sang aita
is_aita = -1: bị aita đánh dấu không phải cntt, k lọc và gửi sang nữa

ALTER TABLE `nv4_vi_project_investment` ADD `is_aita` TINYINT NOT NULL DEFAULT '0' AFTER `name_staff`;
ALTER TABLE `nv4_vi_bidding_plans` ADD `is_aita` TINYINT NOT NULL DEFAULT '0' AFTER `name_staff`;
ALTER TABLE `nv4_vi_bidding_result` ADD `is_aita` TINYINT NOT NULL DEFAULT '0' AFTER `name_staff`;

#17/05/2022: Ton: thêm các dữ liệu phục vụ push dữ liệu nv4_vi_aita_result_id sang AITA

    //Chạy file sendmail-aws\aita\tool_update_nv4_vi_aita_result_id.php để cập nhật bảng nv4_vi_aita_result_id
    //Lưu ý: Mỗi lần em chỉ cho chạy 100 row

ALTER TABLE `nv4_vi_aita_result_id` ADD `is_check` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '0: không cần check\r\n1: cần check lại' AFTER `result_id`;
DROP TABLE IF EXISTS `nv4_vi_aita_result_logs`;
CREATE TABLE IF NOT EXISTS `nv4_vi_aita_result_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from_id` int(11) NOT NULL COMMENT 'id tin bắt đầu',
  `to_id` int(11) NOT NULL COMMENT 'id kết thúc',
  `from_time` int(11) NOT NULL COMMENT 'Thời gian bắt đầu',
  `to_time` int(11) NOT NULL COMMENT 'Thời gian kết thúc',
  `total_time` int(11) NOT NULL COMMENT 'Tổng thời gian',
  `status` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `nv4_vi_aita_result_id`;
CREATE TABLE IF NOT EXISTS `nv4_vi_aita_result_id` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filter_id` int(11) NOT NULL,
  `result_id` int(11) NOT NULL DEFAULT 0,
  `addtime` int(11) NOT NULL DEFAULT 0,
  `send_status` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `result_id` (`result_id`),
  KEY `addtime` (`addtime`),
  KEY `send_status` (`send_status`),
  KEY `filter_id` (`filter_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

#16/05/2022: Ton: thêm các dữ liệu phục vụ push dữ liệu devproject sang AITA

CREATE TABLE IF NOT EXISTS `nv4_vi_aita_devprojects_logs` (
  `devprojects_id` int(11) NOT NULL AUTO_INCREMENT,
  `from_id` int(11) NOT NULL COMMENT 'id tin bắt đầu',
  `to_id` int(11) NOT NULL COMMENT 'id kết thúc',
  `from_time` int(11) NOT NULL COMMENT 'Thời gian bắt đầu',
  `to_time` int(11) NOT NULL COMMENT 'Thời gian kết thúc',
  `total_time` int(11) NOT NULL COMMENT 'Tổng thời gian',
  `status` tinyint(1) NOT NULL,
  PRIMARY KEY (`devprojects_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `nv4_vi_aita_devproject_id` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filter_id` int(11) NOT NULL,
  `devproject_id` int(11) NOT NULL DEFAULT 0,
  `addtime` int(11) NOT NULL DEFAULT 0,
  `send_status` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `devproject_id` (`devproject_id`),
  KEY `addtime` (`addtime`),
  KEY `send_status` (`send_status`),
  KEY `filter_id` (`filter_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COLLATE=utf8mb4_unicode_ci;

#13/05/2022: tuyenhv: thêm các dữ liệu phục vụ push dữ liệu sang AITA
ALTER TABLE `nv4_vi_bidding_row` ADD `is_aita` TINYINT NOT NULL DEFAULT '0' AFTER `province_id`;

DROP TABLE IF EXISTS `nv4_vi_aita_filter`;
CREATE TABLE IF NOT EXISTS `nv4_vi_aita_filter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Tên bộ lọc',
  `key_search` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Từ khóa',
  `key_search2` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `without_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Từ khóa loại trừ',
  `search_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Tìm tương đối/tuyệt đối',
  `status` tinyint(1) NOT NULL COMMENT 'Trạng thái',
  `addtime` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'TG Tạo',
  `edittime` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'TG sửa cuối',
  `weight` mediumint(8) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `nv4_vi_aita_bid_logs`;
CREATE TABLE IF NOT EXISTS `nv4_vi_aita_bid_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from_id` int(11) NOT NULL COMMENT 'id tin bắt đầu',
  `to_id` int(11) NOT NULL COMMENT 'id kết thúc',
  `from_time` int(11) NOT NULL COMMENT 'Thời gian bắt đầu',
  `to_time` int(11) NOT NULL COMMENT 'Thời gian kết thúc',
  `total_time` int(11) NOT NULL COMMENT 'Tổng thời gian',
  `status` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `nv4_vi_aita_bid_id`;
CREATE TABLE IF NOT EXISTS `nv4_vi_aita_bid_id` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filter_id` int(11) NOT NULL,
  `bid_id` int(11) NOT NULL DEFAULT 0,
  `addtime` int(11) NOT NULL DEFAULT 0,
  `send_status` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `bid_id` (`bid_id`),
  KEY `addtime` (`addtime`),
  KEY `send_status` (`send_status`),
  KEY `filter_id` (`filter_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `nv4_vi_aita_plan_logs`;
CREATE TABLE IF NOT EXISTS `nv4_vi_aita_plan_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from_id` int(11) NOT NULL COMMENT 'id tin bắt đầu',
  `to_id` int(11) NOT NULL COMMENT 'id kết thúc',
  `from_time` int(11) NOT NULL COMMENT 'Thời gian bắt đầu',
  `to_time` int(11) NOT NULL COMMENT 'Thời gian kết thúc',
  `total_time` int(11) NOT NULL COMMENT 'Tổng thời gian',
  `status` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `nv4_vi_aita_plan_id`;
CREATE TABLE IF NOT EXISTS `nv4_vi_aita_plan_id` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filter_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL DEFAULT 0,
  `addtime` int(11) NOT NULL DEFAULT 0,
  `send_status` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `plan_id` (`plan_id`),
  KEY `addtime` (`addtime`),
  KEY `send_status` (`send_status`),
  KEY `filter_id` (`filter_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

24/01/2022: Thao: Cập nhật dữ liệu bảng nv4_vi_bidding_row
ALTER TABLE `nv4_vi_bidding_row` ADD `province_id` SMALLINT NOT NULL DEFAULT '0' AFTER `exported_sitemap`

21/01/2022: Tuyên: Đưa bảng nv4_vi_bidding_result_goods lên ES
ALTER TABLE `nv4_vi_bidding_result_goods` ADD `elasticsearch` INT NOT NULL DEFAULT '0' AFTER `type_info`;

31/10/2019: Tuyên: chức năng quét bộ lọc của khách hàng thông thường
INSERT INTO `nv4_config` (`lang`, `module`, `config_name`, `config_value`) VALUES ('vi', 'bidding', 'stat_ufilter_month', '');
INSERT INTO `nv4_config` (`lang`, `module`, `config_name`, `config_value`) VALUES ('vi', 'bidding', 'stat_ufilter_current', '-1');

DROP TABLE IF EXISTS `nv4_vi_bidding_filter_ubymonth`;
CREATE TABLE IF NOT EXISTS `nv4_vi_bidding_filter_ubymonth` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `in_month` varchar(10) NOT NULL DEFAULT '',
  `userid` int(11) NOT NULL DEFAULT '0',
  `filterid` int(11) NOT NULL DEFAULT '0',
  `vip_use` tinyint(4) NOT NULL DEFAULT '1',
  `data` text,
  PRIMARY KEY (`id`),
  KEY `in_month` (`in_month`),
  KEY `userid` (`userid`),
  KEY `filterid` (`filterid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

16/8: Tuyên: VIP 4; Gửi mail dự án
CREATE TABLE IF NOT EXISTS `nv4_vi_bidding_project_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from_id` int(11) NOT NULL DEFAULT '0' COMMENT 'id tin bắt đầu',
  `to_id` int(11) NOT NULL DEFAULT '0' COMMENT 'id kết thúc',
  `from_plan` int(11) NOT NULL DEFAULT '0',
  `to_plan` int(11) NOT NULL DEFAULT '0',
  `from_pq` int(11) NOT NULL DEFAULT '0' COMMENT 'Từ TBMST',
  `to_pq` int(11) NOT NULL DEFAULT '0' COMMENT 'Đến TBMST',
  `begin_cbdmda` int(11) NOT NULL DEFAULT '0',
  `to_cbdmda` int(11) NOT NULL DEFAULT '0',
  `begin_kqlcndt` int(11) NOT NULL DEFAULT '0',
  `to_kqlcndt` int(11) NOT NULL DEFAULT '0',
  `begin_kqst` int(11) NOT NULL DEFAULT '0',
  `to_kqst` int(11) NOT NULL DEFAULT '0',
  `begin_hd` int(11) NOT NULL DEFAULT '0',
  `to_hd` int(11) NOT NULL DEFAULT '0',
  `from_time` int(11) NOT NULL COMMENT 'Thời gian bắt đầu',
  `to_time` int(11) NOT NULL COMMENT 'Thời gian kết thúc',
  `total_time` int(11) NOT NULL COMMENT 'Tổng thời gian',
  `userid` int(11) NOT NULL COMMENT 'Mã khách hàng đang chạy đến nếu  status =0',
  `status` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE IF NOT EXISTS `nv4_vi_bidding_project_id` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` mediumint(8) NOT NULL,
  `filter_id` int(11) NOT NULL,
  `cbdmda_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Công bố danh mục dự án',
  `bid_id` int(11) NOT NULL DEFAULT '0',
  `plan_id` int(11) NOT NULL DEFAULT '0',
  `result_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Kết quả chọn nhà thầu',
  `pq_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Thông báo MST',
  `kqst_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Kết quả sơ tuyển',
  `hd_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Hợp đồng',
  `addtime` int(11) NOT NULL DEFAULT '0',
  `send_status` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `userid` (`userid`),
  KEY `filter_id` (`filter_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Bảng tạm lưu id các tin trước khi chuyển thành mail';
