<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */

// Gửi mail follow bằng điểm
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$create_mail_file = NV_ROOTDIR . '/data/create_mail_tbmt_online_' . $prefix_lang . '_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_tbmt_online_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "tbmt_follow_online.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit();
}

if (file_exists(NV_ROOTDIR . '/data/create_mail_tbmt_online_' . $prefix_lang . '.txt')) {
    file_put_contents(NV_ROOTDIR . '/data/create_mail_tbmt_online_' . $prefix_lang . '.txt', NV_CURRENTTIME);
}

// Khởi tạo biến lưu thông tin thông báo
$arrInform = $list_info = [];
$array_cancel_reason = [
    'TTQDPL' => $lang_module['reason_ttqdpl'],
    'SPTDT' => $lang_module['reason_sptdt'],
    'PVDT' => $lang_module['reason_pvdt'],
    'CT' => $lang_module['reason_ct'],
    'NTCT' => $lang_module['reason_ntct'],
    'KDUN' => $lang_module['reason_kdun'],
    'TDMT' => $lang_module['reason_tdmt'],
    'HSMT' => $lang_module['reason_hsmt']
];
try {
    $user = [];
    // Lấy danh sách follow bằng điểm, chỉ tính các bid_id có filter_id =0
    $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND filter_id = 0 AND remind_hsdt = 0 LIMIT 20');
    $info_user = [];
    while ($row = $query->fetch()) {
        $user[] = $row['userid'];
    }

    // Kiểm tra xem có user theo dõi mua bằng điểm không
    if (!empty($user)) {
        // lấy thông tin user
        $arr_user = $db->query('SELECT userid, username, email, first_name, last_name FROM nv4_users WHERE userid IN (' . implode(',', $user) . ')');

        while ($row = $arr_user->fetch()) {
            $info_user[$row['userid']] = $row;
        }

        // Lấy thêm thông tin từ bảng customs
        if (!empty($info_user)) {
            $sql_customs = 'SELECT id as cusid, user_id, vip, sub_email, phone, active_user FROM nv4_bidding_customs WHERE user_id IN (' . implode(',', array_keys($info_user)) . ')';

            $query_customs = $db->query($sql_customs);
            while ($row = $query_customs->fetch()) {
                if (isset($info_user[$row['user_id']])) {
                    $info_user[$row['user_id']]['vip'] = $row['vip'];
                    $info_user[$row['user_id']]['sub_email'] = $row['sub_email'];
                    $info_user[$row['user_id']]['phone'] = $row['phone'];
                    $info_user[$row['user_id']]['cusid'] = $row['cusid'];
                    $info_user[$row['user_id']]['active_user'] = $row['active_user'];
                }
            }

            // Xử lý phân quyền email
            foreach ($info_user as $userid => $user_data) {
                if ($user_data['active_user'] == 1 && $user_data['cusid'] > 0) {
                    $arr_subemail = [];
                    $query_permission = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_permission WHERE vipid = ' . $user_data['cusid'] . ' AND totime > ' . NV_CURRENTTIME . ' AND send_mail = 1 AND prefix_lang = ' . $prefix_lang);

                    while ($permission = $query_permission->fetch()) {
                        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid = ' . $permission['userid']);
                        while ($user = $arr_user->fetch()) {
                            $arr_subemail[$user['email']] = $user['email'];
                        }
                    }

                    if (!empty($arr_subemail)) {
                        $info_user[$userid]['sub_email'] = implode(',', $arr_subemail);
                    }
                }
            }
        }

        if (!empty($info_user)) {
            // Lấy ra tất cả tin đấu thầu trong danh sách gửi đi
            foreach ($info_user as $userid => $v) {
                if (file_exists($create_mail_file)) {
                    file_put_contents($create_mail_file, 'BEGIN: ' . $userid . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
                }

                $arr_bid = [];
                $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row a INNER JOIN ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail b ON a.id=b.id WHERE a.id IN (SELECT bid_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND remind_hsdt = 0 AND bid_id>0 AND userid=' . $userid . ')');

                while ($bid_row = $query->fetch()) {
                    $arr_bid[$bid_row['id']] = $bid_row;
                }

                // Gộp nhóm những mail chung chủ đề lại theo từng VIP
                $array_bid_id = [];
                $array_updated_bid_id = []; // Mảng chứa những ID đã được cập nhật send_status riêng lẻ
                $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND remind_hsdt = 0 AND userid=' . $userid . ' AND filter_id = 0 LIMIT 50');

                while ($bid_data = $query->fetch()) {
                    $array_bid_id[$bid_data['id']] = $bid_data['id'];

                    if ($bid_data['bid_id'] > 0) {
                        if ($bid_data['petition_row_id'] > 0) {
                            $v['petition'][$bid_data['bid_id']] = $bid_data;
                        } elseif (!empty($bid_data['clarify_id'])) {
                            $v['yclr'][$bid_data['bid_id']] = $bid_data;
                        } elseif ($bid_data['reasonid'] > 0) {
                            $v['cancel'][$bid_data['bid_id']] = $bid_data;
                        } elseif ($bid_data['notify_vip'] != '') {
                            $v['notify'][$bid_data['bid_id']] = $bid_data;
                        } elseif ($bid_data['reoffer_result_id'] > 0) {
                            $v['reoffer_result'][$bid_data['bid_id']] = $bid_data;
                        } elseif ($bid_data['reoffer_room_id'] > 0) {
                            $v['reoffer_room'][$bid_data['bid_id']] = $bid_data;
                        } else {
                            $v['follow'][]['list_bid'][] = $arr_bid[$bid_data['bid_id']];
                            $v['bid'][$bid_data['bid_id']] = $bid_data;
                        }
                    }

                    if ($bid_data['result_id'] > 0) {
                        $v['result'][] = $bid_data['result_id'];
                    }

                    if ($bid_data['kqmt_id'] > 0) {
                        $v['result_open'][] = $bid_data['kqmt_id'];
                    }
                }
                $db->beginTransaction();
                $v['sub_email'] = isset($info_user[$userid]['sub_email']) ? $info_user[$userid]['sub_email'] : '';
                $v['phone'] = isset($info_user[$userid]['phone']) ? $info_user[$userid]['phone'] : '';

                try {
                    $data_insert = [];
                    $data_insert['addtime'] = NV_CURRENTTIME;
                    $data_insert['send_time'] = 0;
                    $data_insert['status'] = 0;

                    // Gửi mail thông báo mới thầu, kế hoạch chọn nhà thầu, thông báo sơ tuyển nhóm theo từng bộ lọc
                    if (!empty($v['follow'])) {
                        $data_insert['title'] = sprintf($lang_module['mail_title_tbmt'], date('d/m/Y'));
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        if (file_exists($create_mail_file)) {
                            file_put_contents($create_mail_file, "Begin nv_theme_bidding_mail_follow\n", FILE_APPEND);
                        }

                        $data_insert['content'] = nv_theme_bidding_mail_follow($v['follow'], $userid, $arrInform, $v['username']);

                        file_put_contents($create_mail_file, "END nv_theme_bidding_mail_follow\n", FILE_APPEND);
                        // Nội dung htm sẽ gửi cho từng khách
                        try {
                            file_put_contents($create_mail_file, "BIGIN INSERT INTO nv4_vi_bidding_mail\n", FILE_APPEND);
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR);
                            $stmt->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail tin mới vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail 118: " . $userid . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage());
                            break;
                        }
                    }

                    // Gửi mail kết quả đấu thầu ra riêng một mail cho các VIP
                    if (!empty($v['result'])) {
                        $data_insert['title'] = sprintf($lang_module['mail_title_kqlcnt'], date('d/m/Y'));
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_result_mail($v['result'], $userid, $arrInform, $v['username']);

                        // Nội dung htm sẽ gửi cho từng khách.
                        try {
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                            $stmt->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 148 " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail: 148 " . $userid . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }

                    // Gửi mail kết quả mở thầu ra riêng một mail cho các VIP
                    if (!empty($v['result_open'])) {
                        $data_insert['title'] = sprintf($lang_module['mail_title_kqmt'], date('d/m/Y'));
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_result_open_mail($v['result_open'], $userid, $arrInform, $v['username']);

                        try {
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                            $stmt->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 148 " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail: 148 " . $userid . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }

                    // Gửi mail thay đổi
                    if (!empty($v['notify'])) {
                        $first_notify = reset($v['notify']);
                        $bid_info = $db->query('SELECT so_tbmt FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id = ' . $first_notify['bid_id'])->fetch();
                        $data_insert['title'] = sprintf($lang_module['mail_title_extend'], $bid_info['so_tbmt'], date('d/m/Y'));
                        $data_insert['type'] = 1;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_extend_mail($v['notify'], $userid, $arrInform, $v['username']);

                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                            $stmt1->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    // Gửi mail hủy thầu nếu có
                    if (!empty($v['cancel'])) {
                        $first_cancel = reset($v['cancel']);
                        $bid_info = $db->query('SELECT so_tbmt FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id = ' . $first_cancel['bid_id'])->fetch();
                        $data_insert['title'] = sprintf($lang_module['mail_title_cancel'], $bid_info['so_tbmt'], date('d/m/Y'));
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_cancel_mail($v['cancel'], $userid, $arrInform, $v['username']);

                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                            $stmt1->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    // Gửi mail yêu cầu làm rõ nếu có
                    if (!empty($v['yclr'])) {
                        $first_yclr = reset($v['yclr']);
                        $bid_info = $db->query('SELECT so_tbmt FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id = ' . $first_yclr['bid_id'])->fetch();
                        $data_insert['title'] = sprintf($lang_module['mail_title_yclr'], $bid_info['so_tbmt'], date('d/m/Y'));
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_yclr_mail($v['yclr'], $userid, $arrInform, $v['username']);

                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                            $stmt1->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id = ' . $first_yclr['id']);
                            $array_updated_bid_id[] = $first_yclr['id']; // Đánh dấu đã cập nhật
                        } catch (PDOException $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    // Gửi mail kiến nghị nếu có
                    if (!empty($v['petition'])) {
                        $first_petition = reset($v['petition']);
                        $bid_info = $db->query('SELECT so_tbmt FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id = ' . $first_petition['bid_id'])->fetch();
                        $data_insert['title'] = sprintf($lang_module['mail_title_petition'], $bid_info['so_tbmt']);
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_petition_mail($v['petition'], $userid, $arrInform, $v['username']);
                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id = ' . $first_petition['id']);
                            $array_updated_bid_id[] = $first_petition['id']; // Đánh dấu đã cập nhật
                        } catch (PDOException $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    // Gửi mail kết quả chào giá trực tuyến
                    if (!empty($v['reoffer_result'])) {
                        $first_reoffer_result = reset($v['reoffer_result']);
                        $bid_info = $db->query('SELECT so_tbmt FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id = ' . $first_reoffer_result['bid_id'])->fetch();
                        $data_insert['title'] = sprintf($lang_module['mail_title_reoffer_result'], $bid_info['so_tbmt'], date('d/m/Y'));
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_reoffer_result_mail($v['reoffer_result'], $userid, $arrInform, $v['username']);

                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id = ' . $first_reoffer_result['id']);
                            $array_updated_bid_id[] = $first_reoffer_result['id']; // Đánh dấu đã cập nhật
                        } catch (PDOException $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    // Gửi mail phòng chào giá trực tuyến
                    if (!empty($v['reoffer_room'])) {
                        $first_reoffer_room = reset($v['reoffer_room']);
                        $bid_info = $db->query('SELECT so_tbmt FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id = ' . $first_reoffer_room['bid_id'])->fetch();
                        $data_insert['title'] = sprintf($lang_module['mail_title_reoffer_room'], $bid_info['so_tbmt'], date('d/m/Y'));
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_reoffer_room_mail($v['reoffer_room'], $userid, $arrInform, $v['username']);

                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id = ' . $first_reoffer_room['id']);
                            $array_updated_bid_id[] = $first_reoffer_room['id']; // Đánh dấu đã cập nhật
                        } catch (PDOException $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    // Thực hiện lưu biến thông báo tạm
                    foreach ($arrInform as $k => $v) {
                        foreach ($v as $v1) {
                            $list_info[] = $v1;
                        }
                    }

                    // Reset lại biến này về rỗng để tiến hành lưu mảng dữ liệu tiếp theo
                    $arrInform = [];

                    echo "userid = " . $userid . "\n";

                    // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                    if (!empty($array_bid_id) and !empty($_mailid)) {
                        // Loại trừ những ID đã được cập nhật riêng lẻ (KQCGTT, phòng chào giá, YCLR, kiến nghị)
                        $remaining_bid_ids = array_diff($array_bid_id, $array_updated_bid_id);

                        file_put_contents($create_mail_file, "Remaining bid_ids: " . count($remaining_bid_ids) . " - " . implode(',', $remaining_bid_ids) . "\n", FILE_APPEND);

                        if (!empty($remaining_bid_ids)) {
                            // Chỉ update cho những tin thường (không phải kiến nghị, YCLR, KQCGTT, phòng chào giá)
                            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $remaining_bid_ids) . ') AND send_status = 0 AND remind_hsdt = 0 AND petition_row_id = 0 AND (clarify_id = "" OR clarify_id IS NULL) AND reoffer_result_id = 0 AND (reoffer_room_id = "" OR reoffer_room_id IS NULL OR reoffer_room_id = 0)');
                        }
                    }
                    $db->commit();
                    file_put_contents($create_mail_file, "number bid = " . sizeof($array_bid_id) . ": " . implode(',', $array_bid_id) . "\n", FILE_APPEND);
                    file_put_contents($create_mail_file, "END: " . $userid . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                } catch (PDOException $e) {
                    file_put_contents($create_mail_file, 'rollBack: ' . $userid . "\n\n", FILE_APPEND);
                    $db->rollBack();
                    echo '<pre>';
                    print_r($e);
                }
            }
        }
    }

    if (file_exists(NV_ROOTDIR . '/data/create_mail_tbmt_online' . $prefix_lang . '.txt')) {
        unlink(NV_ROOTDIR . '/data/create_mail_tbmt_online' . $prefix_lang . '.txt');
    }
} catch (PDOException $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR: " . print_r($e, true) . "\n\n");
}

// Thực hiện lưu thông báo
if (!empty($list_info)) {
    echo "Có tổng " . sizeof($list_info) . ' Thông báo Inform';
    file_put_contents(NV_ROOTDIR . '/data/inform/inform_tbmt_follow_online' . uniqid('', true) . '.txt', json_encode($list_info));
}

echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function nv_theme_result_mail($array_result, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;
    if (!empty($array_result)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);

        // Lấy thông tin các kêt quả chọn nhà thầu
        $list_result = [];
        $array_result = implode(',', $array_result);
        $array_codetbmt = '';
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result WHERE id IN (' . $array_result . ')');
        while ($row = $result->fetch()) {
            $list_result[$row['id']] = $row;
            $tbmt = explode('-', $row['code']);
            $array_codetbmt = $tbmt[0];
        }
        $array_result_ids = array_keys($list_result);
        if (!empty($array_result_ids)) {
            $result_query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result_business WHERE resultid IN (' . implode(',', $array_result_ids) . ')');
            while ($business_row = $result_query->fetch()) {
                $list_result[$business_row['resultid']]['business'][] = $business_row;
            }
        }
        // chỉ lấy đại diện 1 tin theo dõi
        $mail_footer_follow = '';
        if (!empty($array_codetbmt)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
            if ($row = $result->fetch()) {
                $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
            }
        }

        if (!empty($list_result)) {
            foreach ($list_result as $data) {
                $data['title_a'] = nv_htmlspecialchars($data['title']);
                $data['post_time'] = $data['post_time'] > 0 ? nv_date('H:i d/m/Y', $data['post_time']) : '';
                $data['finish_time'] = $data['finish_time'] > 0 ? nv_date('d/m/Y', $data['finish_time']) : '';
                $luachon_t = getUrlByLanguage('kqlcnt');
                if ($site_lang != 'vi') {
                    $data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['result'] . '/' . $luachon_t . '/' . $data['alias'] . '-' . $data['id'] . '.html';
                } else {
                    $data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['result'] . '/' . $luachon_t . '/' . $data['alias'] . '-' . $data['id'] . '.html';
                }

                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_search_by_result'), $data['title']),
                    'en' => sprintf(get_lang('en', 'title_search_by_result'), $data['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?kq=' . $data['id'],
                    'en' => URL_RE . 'en/?kq=' . $data['id']
                ];

                // Thông báo Push Notification cho người dùng
                // insertInform($vip_id, $arrMess, $data['link_view']);
                $arrInform['nv_theme_result_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];
                if ($data['tender_price'] > 0) {
                    $data['tender_price'] = number_format($data['tender_price'], 0, ',', '.') . ' VND';
                }
                if ($data['bid_price'] > 0 and is_numeric($data['bid_price'])) {
                    $data['bid_price'] = number_format($data['bid_price'], 0, ',', '.') . ' VND';
                }
                $xtpl->assign('DATA', $data);
                if (!empty($data['business'])) {
                    foreach ($data['business'] as $business) {
                        $business['bidwinningprice'] = number_format($business['bidwinningprice'], 0, '.', ',') . ' VND';
                        $xtpl->assign('BUSINESS', $business);
                        $xtpl->parse('main.result.loop.business_winning_price.business');
                    }
                    $xtpl->parse('main.result.loop.business_winning_price');
                }
                if (!empty($data['code'])) {
                    $xtpl->parse('main.result.loop.code');
                }
                if (!empty($data['investor'])) {
                    $xtpl->parse('main.result.loop.chu_dau_tu');
                }
                if (!empty($data['type_bid'])) {
                    $xtpl->parse('main.result.loop.type_bid');
                }
                if (!empty($data['cat'])) {
                    $xtpl->parse('main.result.loop.cat');
                }
                if (!empty($data['title'])) {
                    $xtpl->parse('main.result.loop.title');
                }
                if (!empty($data['bid_price'])) {
                    $xtpl->parse('main.result.loop.bid_price');
                }
                if (!empty($data['finish_time'])) {
                    $xtpl->parse('main.result.loop.finish_time');
                }
                if (!empty($data['post_time'])) {
                    $xtpl->parse('main.result.loop.post_time');
                }

                if (!empty($data['point'])) {
                    $xtpl->parse('main.result.loop.point');
                }
                if (!empty($data['evaluating_price'])) {
                    $xtpl->parse('main.result.loop.evaluating_price');
                }
                $xtpl->parse('main.result.loop');
            }
            $xtpl->parse('main.result');
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_result_open_mail($array_result, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;
    if (!empty($array_result)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);

        // Lấy thông tin các kêt quả chọn nhà thầu
        $list_result = $list_empty_chu_dau_tu = $array_codetbmt = [];
        $array_result = implode("','", $array_result);
        $result = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_" . BIDDING_MODULE . "_open WHERE so_tbmt IN ('" . $array_result . "')");
        while ($row = $result->fetch()) {
            $list_result[$row['so_tbmt']] = $row;
            $tbmt = explode('-', $row['so_tbmt']);
            $array_codetbmt = $tbmt[0];
            if (empty($row['chu_dau_tu'])) {
                $list_empty_chu_dau_tu[] = '"' . $row['so_tbmt'] . '"';
            }
        }

        // chỉ lấy đại diện 1 tin theo dõi
        $mail_footer_follow = '';
        if (!empty($array_codetbmt)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
            if ($row = $result->fetch()) {
                $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
            }
        }

        // Lấy tên chủ đầu tư trường hợp emty trong nv4_bidding_solicitor
        if (!empty($list_empty_chu_dau_tu)) {
            $list_solicitor_id = [];
            $result = $db->query('SELECT t1.solicitor_id, t1.so_tbmt FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row as t1 INNER JOIN ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail as t2 ON t1.id=t2.id WHERE t1.so_tbmt IN (' . implode(',', $list_empty_chu_dau_tu) . ')');
            while ($row = $result->fetch()) {
                if (!empty($row['solicitor_id'])) {
                    $list_solicitor_id[(int) $row['solicitor_id']] = $row['so_tbmt'];
                }
            }
            if (!empty($list_solicitor_id)) {
                $result = $db->query('SELECT id, title FROM ' . BID_PREFIX_GLOBAL . '_solicitor WHERE id IN (' . implode(',', array_keys($list_solicitor_id)) . ')');
                while ($row = $result->fetch()) {
                    $list_result[$list_solicitor_id[$row['id']]]['chu_dau_tu'] = $row['title'];
                }
            }
        }
        /*
         * $result_detail = $db->query("SELECT * FROM nv4_vi_bidding_open_detail WHERE so_tbmt IN ('" . $array_result . "')");
         * while ($row = $result_detail->fetch()) {
         * $list_result_detail[] = $row;
         * }
         */

        if (!empty($list_result)) {
            foreach ($list_result as $data) {
                $data['title_a'] = nv_htmlspecialchars($data['goi_thau']);
                $data['thoi_diem_mo_thau'] = $data['thoi_diem_mo_thau'] > 0 ? nv_date('H:i d/m/Y', $data['thoi_diem_mo_thau']) : '';
                $data['thoi_diem_hoan_thanh'] = $data['thoi_diem_hoan_thanh'] > 0 ? nv_date('d/m/Y', $data['thoi_diem_hoan_thanh']) : '';
                $data['trang_thai'] = $lang_module['kqmt_status' . $data['trang_thai']];
                $data['gia_goi_thau'] = number_format($data['gia_goi_thau']);
                /*
                 * foreach ($list_result_detail as $result_detail) {
                 * $xtpl->assign('DETAIL', $result_detail);
                 * $xtpl->parse('main.result_open.loop.detail');
                 * }
                 */
                if ($site_lang != 'vi') {
                    $data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['viewopen'] . '/' . $data['alias'] . '-' . $data['so_tbmt'] . '.html';
                } else {
                    $data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['viewopen'] . '/' . $data['alias'] . '-' . $data['so_tbmt'] . '.html';
                }

                $xtpl->assign('DATA', $data);

                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_search_by_result_open'), $data['title']),
                    'en' => sprintf(get_lang('en', 'title_search_by_result_open'), $data['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?mt=' . $data['so_tbmt'],
                    'en' => URL_RE . 'en/?mt=' . $data['so_tbmt']
                ];

                // Thông báo Push Notification cho người dùng
                // insertInform($vip_id, $arrMess, $data['link_view']);
                $arrInform['nv_theme_result_open_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                if (!empty($data['chu_dau_tu'])) {
                    $xtpl->parse('main.result_open.loop.chu_dau_tu');
                }
                if (!empty($data['ben_moi_thau'])) {
                    $xtpl->parse('main.result_open.loop.ben_moi_thau');
                }
                if (!empty($data['trang_thai'])) {
                    $xtpl->parse('main.result_open.loop.trang_thai');
                }
                if (!empty($data['thoi_diem_mo_thau'])) {
                    $xtpl->parse('main.result_open.loop.thoi_diem_mo_thau');
                }
                if (!empty($data['thoi_diem_hoan_thanh'])) {
                    $xtpl->parse('main.result_open.loop.thoi_diem_hoan_thanh');
                }
                if (!empty($data['gia_goi_thau'])) {
                    $xtpl->parse('main.result_open.loop.gia_goi_thau');
                }
                if (!empty($data['so_luong_nha_thau'])) {
                    $xtpl->parse('main.result_open.loop.so_luong_nha_thau');
                }
                $xtpl->parse('main.result_open.loop');
            }
            $xtpl->parse('main.result_open');
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_bidding_mail_follow($array_follow, $userid, &$arrInform, $username)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func;

    $arr_field = [
        1 => 'Hàng hóa',
        2 => 'Xây lắp',
        3 => 'Tư vấn',
        4 => 'Phi tư vấn',
        5 => 'Hỗn hợp'
    ];
    $array_codetbmt = '';
    if (!empty($array_follow)) {
        $xtpl = new XTemplate('email_follow_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        foreach ($array_follow as $arr_list) {
            if (!empty($arr_list['list_bid'])) {
                $i_break = 0;
                // lọc bỏ tin trùng nhau
                $arr_stbmt = [];

                foreach ($arr_list['list_bid'] as $array_data) {
                    $array_data['so_tbmt'] = explode('-', $array_data['so_tbmt']);
                    if (!isset($arr_stbmt[$array_data['so_tbmt'][0]])) {
                        $arr_stbmt[$array_data['so_tbmt'][0]] = $array_data['so_tbmt'][1];
                    } else {
                        $array_data['so_tbmt'][1] = intval($array_data['so_tbmt'][1]);
                        $old = intval($arr_stbmt[$array_data['so_tbmt'][0]]);
                        if ($old < $array_data['so_tbmt'][1]) {
                            $arr_stbmt[$array_data['so_tbmt'][0]] = '0' . $array_data['so_tbmt'][1];
                        }
                    }
                }
                // hiển thị
                foreach ($arr_list['list_bid'] as $array_data) {
                    $so_tbmt = explode('-', $array_data['so_tbmt']);
                    $array_codetbmt = $so_tbmt[0];
                    if (isset($arr_stbmt[$so_tbmt[0]]) && intval($arr_stbmt[$so_tbmt[0]]) == intval($so_tbmt[1])) {
                        ++$i_break;
                        if ($i_break > 50) {
                            break;
                        }
                        $array_data['title'] = $array_data['goi_thau'];
                        $array_data['title_a'] = nv_htmlspecialchars($array_data['goi_thau']);

                        $array_data['ngay_dang_tai'] = nv_date('H:i d/m/y', $array_data['ngay_dang_tai']);
                        $tbmt_t = getUrlByLanguage('tbmt');
                        if ($site_lang != 'vi') {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                        } else {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                        }

                        $check = md5($userid . $so_tbmt[0]);
                        $array_data['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/follow?bid_id=' . $array_data['id'] . '&bid_code=' . $so_tbmt[0] . '&vipid=' . $userid . '&check=' . $check;

                        $xtpl->assign('DATA', $array_data);
                        $arrMess = [
                            'vi' => sprintf(get_lang('vi', 'title_new_tbmt'), $array_data['title']),
                            'en' => sprintf(get_lang('en', 'title_new_tbmt'), $array_data['title'])
                        ];

                        $arrLink = [
                            'vi' => URL_RE . '?tb=' . $array_data['id'],
                            'en' => URL_RE . 'en/?tb=' . $array_data['id']
                        ];

                        // Thông báo Push Notification cho người dùng
                        // insertInform($userid, $arrMess, $array_data['link_view']);
                        $arrInform['nv_theme_bidding_mail_follow'][] = [
                            'vip_id' => $userid,
                            'mess' => $arrMess,
                            'link' => $arrLink
                        ];

                        if ($array_data['chu_dau_tu'] != '') {
                            $xtpl->parse('main.follow.content.chu_dau_tu');
                        }

                        if ($array_data['ten_du_an'] != '') {
                            $xtpl->parse('main.follow.content.ten_du_an');
                        }

                        // Kiểm tra xem có giá gói thầu k
                        if ($array_data['price'] > 0) {
                            $xtpl->assign('PRICE_FM', number_format($array_data['price'], 0, ',', '.') . ' VND');
                            $xtpl->parse('main.follow.content.price');
                        }

                        if ($array_data['notify_chance_time'] == 1) {
                            $content_chance = $db->query('SELECT content_chance FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_update WHERE bid_id=' . $array_data['id'] . ' ORDER BY addtime DESC')->fetchColumn();
                            $xtpl->assign('CONTENT_CHANCE', $content_chance);
                            $xtpl->parse('main.follow.content.notify_chance_time');
                        } else {
                            // Tạo link theo dõi tin thầu
                            $xtpl->parse('main.follow.content.follow');
                        }

                        if (intval($so_tbmt[1]) > 0) {
                            $xtpl->parse('main.follow.content.notify_old');
                        }

                        if ($array_data['note'] != '') {
                            $xtpl->parse('main.follow.content.note');
                        }

                        $xtpl->parse('main.follow.content');
                    }
                }
            }
            $xtpl->parse('main.follow');
        }
    }

    // chỉ lấy đại diện 1 tin theo dõi
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_extend_mail($array_extend, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;

    if (!empty($array_extend)) {
        $xtpl = new XTemplate('tbmt_follow_extend.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        $array_codetbmt = '';

        $arr_bid_ids = [];
        foreach ($array_extend as $bid_data) {
            if (!empty($bid_data['bid_id'])) {
                $arr_bid_ids[] = $bid_data['bid_id'];
            }
        }

        $arr_bids = [];
        $arr_details = [];
        if (!empty($arr_bid_ids)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id IN (' . implode(',', $arr_bid_ids) . ')');
            while ($row = $result->fetch()) {
                $arr_bids[$row['id']] = $row;
                $tbmt = explode('-', $row['so_tbmt']);
                $array_codetbmt = $tbmt[0];
            }

            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail WHERE id IN (' . implode(',', $arr_bid_ids) . ')');
            while ($row = $result->fetch()) {
                $arr_details[$row['id']] = $row;
            }
        }

        foreach ($array_extend as $bid_id => $bid_data) {
            if (isset($arr_bids[$bid_id]) && isset($arr_details[$bid_id])) {
                $row = $arr_bids[$bid_id];
                $detail = $arr_details[$bid_id];

                $list_delay = json_decode($detail['list_delay'], true);
                if (!empty($list_delay)) {
                    $delay = end($list_delay);

                    $array_data = array_merge($row, $delay);
                    $array_data['title'] = $array_data['goi_thau'];
                    $array_data['title_a'] = nv_htmlspecialchars($array_data['goi_thau']);
                    $array_data['time_close_old'] = nv_date('H:i d/m/Y', $array_data['time_close_old']);
                    $array_data['time_close_new'] = nv_date('H:i d/m/Y', $array_data['time_close_new']);
                    $array_data['time_open_old'] = nv_date('H:i d/m/Y', $array_data['time_open_old']);
                    $array_data['time_open_new'] = nv_date('H:i d/m/Y', $array_data['time_open_new']);

                    $tbmt_t = getUrlByLanguage('tbmt');
                    if ($site_lang != 'vi') {
                        $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                    } else {
                        $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                    }

                    $array_data['title_extend'] = sprintf($lang_module['title_extend_tbmt'], date('d/m/Y'));
                    $xtpl->assign('DATA', $array_data);

                    $arrMess = [
                        'vi' => sprintf(get_lang('vi', 'title_extend_tbmt'), $array_data['title']),
                        'en' => sprintf(get_lang('en', 'title_extend_tbmt'), $array_data['title'])
                    ];

                    $arrLink = [
                        'vi' => URL_RE . '?tb=' . $array_data['id'],
                        'en' => URL_RE . 'en/?tb=' . $array_data['id']
                    ];

                    $arrInform['nv_theme_extend_mail'][] = [
                        'vip_id' => $vip_id,
                        'mess' => $arrMess,
                        'link' => $arrLink
                    ];

                    if ($array_data['reason'] != '') {
                        $xtpl->parse('main.extend.content.reason');
                    }

                    $xtpl->parse('main.extend.content');
                }
            }
        }
        $xtpl->parse('main.extend');
    }

    // Lấy thông tin follow một lần
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt));
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_cancel_mail($array_cancel, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func, $array_cancel_reason;

    if (!empty($array_cancel)) {
        $xtpl = new XTemplate('tbmt_follow_huythau.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        $array_codetbmt = '';

        $arr_bid_ids = [];
        foreach ($array_cancel as $bid_data) {
            if (!empty($bid_data['bid_id'])) {
                $arr_bid_ids[] = $bid_data['bid_id'];
            }
        }

        $arr_bids = [];
        $arr_details = [];
        if (!empty($arr_bid_ids)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id IN (' . implode(',', $arr_bid_ids) . ')');
            while ($row = $result->fetch()) {
                $arr_bids[$row['id']] = $row;
                $tbmt = explode('-', $row['so_tbmt']);
                $array_codetbmt = $tbmt[0];
            }

            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail WHERE id IN (' . implode(',', $arr_bid_ids) . ')');
            while ($row = $result->fetch()) {
                $arr_details[$row['id']] = $row;
            }
        }

        foreach ($array_cancel as $bid_id => $bid_data) {
            if (isset($arr_bids[$bid_id]) && isset($arr_details[$bid_id])) {
                $row = $arr_bids[$bid_id];
                $detail = $arr_details[$bid_id];

                $array_data = array_merge($row, $detail);
                $array_data['title'] = $array_data['goi_thau'];
                $array_data['title_a'] = nv_htmlspecialchars($array_data['goi_thau']);
                $array_data['decision_date'] = nv_date('H:i d/m/Y', $array_data['cancel_date']);

                if (!empty($array_data['reason_cancel'])) {
                    $array_data['reason'] = $array_cancel_reason[$array_data['reason_cancel']];
                }

                $tbmt_t = getUrlByLanguage('tbmt');
                if ($site_lang != 'vi') {
                    $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                } else {
                    $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                }

                $xtpl->assign('DATA', $array_data);

                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_cancel_tbmt'), $array_data['title']),
                    'en' => sprintf(get_lang('en', 'title_cancel_tbmt'), $array_data['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?tb=' . $array_data['id'],
                    'en' => URL_RE . 'en/?tb=' . $array_data['id']
                ];

                $arrInform['nv_theme_cancel_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                if (!empty($array_data['reason'])) {
                    $xtpl->parse('main.cancel.content.reason');
                }

                $xtpl->parse('main.cancel.content');
            }
        }
        $xtpl->parse('main.cancel');
    }

    // Lấy thông tin follow một lần
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt));
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

/**
 * Hàm xử lý nội dung email yêu cầu làm rõ
 */
function nv_theme_yclr_mail($yclr_data, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;

    if (!empty($yclr_data)) {
        $xtpl = new XTemplate('tbmt_follow_yclr.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        $array_codetbmt = '';

        $arr_clarify_ids = [];
        $arr_bid_ids = [];
        foreach ($yclr_data as $bid_data) {
            if (!empty($bid_data['clarify_id'])) {
                $arr_clarify_ids[] = $db->quote($bid_data['clarify_id']);
            }
            if (!empty($bid_data['bid_id'])) {
                $arr_bid_ids[] = $bid_data['bid_id'];
            }
        }

        $arr_clarifies = [];
        if (!empty($arr_clarify_ids)) {
            $clarify_result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row_clarify WHERE clarify_id IN (' . implode(',', $arr_clarify_ids) . ')');
            while ($clarify = $clarify_result->fetch()) {
                $arr_clarifies[$clarify['clarify_id']] = $clarify;
            }
        }

        $arr_bids = [];
        if (!empty($arr_bid_ids)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id IN (' . implode(',', $arr_bid_ids) . ')');
            while ($row = $result->fetch()) {
                $arr_bids[$row['id']] = $row;
                $tbmt = explode('-', $row['so_tbmt']);
                $array_codetbmt = $tbmt[0];
            }
        }

        foreach ($yclr_data as $bid_id => $bid_data) {
            if (isset($arr_clarifies[$bid_data['clarify_id']]) && isset($arr_bids[$bid_data['bid_id']])) {
                $clarify = $arr_clarifies[$bid_data['clarify_id']];
                $row = $arr_bids[$bid_data['bid_id']];

                $array_data = array_merge($row, $clarify);
                $array_data['title'] = $array_data['goi_thau'];
                $array_data['title_a'] = nv_htmlspecialchars($array_data['goi_thau']);

                $tbmt_t = getUrlByLanguage('tbmt');
                if ($site_lang != 'vi') {
                    $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['bid_id'] . '.html';
                } else {
                    $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['bid_id'] . '.html';
                }

                $xtpl->assign('DATA', $array_data);

                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_yclr_tbmt'), $array_data['title']),
                    'en' => sprintf(get_lang('en', 'title_yclr_tbmt'), $array_data['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?tb=' . $array_data['bid_id'],
                    'en' => URL_RE . 'en/?tb=' . $array_data['bid_id']
                ];

                $arrInform['nv_theme_yclr_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                $clarify_data = [
                    'req_name' => $clarify['req_name'],
                    'sign_req_date' => nv_date('H:i d/m/Y', $clarify['sign_req_date']),
                    'sign_res_date' => !empty($clarify['sign_res_date']) ? nv_date('H:i d/m/Y', $clarify['sign_res_date']) : ''
                ];

                if (!empty($clarify['clarify_req_content'])) {
                    $req_content = json_decode($clarify['clarify_req_content'], true);
                    if (!empty($req_content)) {
                        $clarify_content = '';
                        foreach ($req_content as $item) {
                            if (!empty($item['subjectName'])) {
                                $clarify_content .= $item['subjectName'] . ":\n";
                            }
                            if (!empty($item['question'])) {
                                $clarify_content .= $item['question'] . "\n\n";
                            }
                        }
                        $clarify_data['clarify_content'] = $clarify_content;
                    } else {
                        $clarify_data['clarify_content'] = $clarify['clarify_req_content'];
                    }
                }

                if (!empty($clarify['clarify_res_content'])) {
                    $res_content = json_decode($clarify['clarify_res_content'], true);
                    if (!empty($res_content)) {
                        $response_content = '';
                        foreach ($res_content as $item) {
                            if (!empty($item['response'])) {
                                $response_content .= $item['response'] . "\n\n";
                            }
                        }
                        $clarify_data['clarify_response'] = $response_content;
                    } else {
                        $clarify_data['clarify_response'] = $clarify['clarify_res_content'];
                    }
                }

                $xtpl->assign('CLARIFY', $clarify_data);

                if (!empty($clarify_data['clarify_response'])) {
                    $xtpl->parse('main.yclr.content.clarify.clarify_response');
                }

                if (!empty($clarify_data['sign_res_date'])) {
                    $xtpl->parse('main.yclr.content.clarify.sign_res_date');
                }

                $xtpl->parse('main.yclr.content.clarify');
                $xtpl->parse('main.yclr.content');
            }
        }
        $xtpl->parse('main.yclr');
    }

    // Chỉ lấy đại diện 1 tin theo dõi
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt));
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_petition_mail($petition_data, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;

    if (!empty($petition_data)) {
        $xtpl = new XTemplate('tbmt_follow_petition.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        $array_codetbmt = '';

        $arr_petition_ids = [];
        $arr_bid_ids = [];
        foreach ($petition_data as $bid_data) {
            if (!empty($bid_data['petition_row_id'])) {
                $arr_petition_ids[] = $bid_data['petition_row_id'];
            }
            if (!empty($bid_data['bid_id'])) {
                $arr_bid_ids[] = $bid_data['bid_id'];
            }
        }

        $arr_petitions = [];
        if (!empty($arr_petition_ids)) {
            $petition_result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row_petition WHERE id IN (' . implode(',', $arr_petition_ids) . ')');
            while ($petition = $petition_result->fetch()) {
                $arr_petitions[$petition['id']] = $petition;
            }
        }

        $arr_req_nos = [];
        foreach ($arr_petitions as $petition) {
            if ($petition['is_reply'] == 2) {
                $arr_req_nos[] = $db->quote($petition['req_no']);
            }
        }
        if (!empty($arr_req_nos)) {
            $sql_original = 'SELECT req_no, req_content, req_date FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row_petition WHERE req_no IN (' . implode(',', $arr_req_nos) . ') AND is_reply != 2';
            $result = $db->query($sql_original);
            while ($row = $result->fetch()) {
                foreach ($arr_petitions as $id => $petition) {
                    if ($petition['is_reply'] == 2 && $petition['req_no'] == $row['req_no']) {
                        $arr_petitions[$id]['req_content'] = $row['req_content'];
                        $arr_petitions[$id]['req_date'] = $row['req_date'];
                    }
                }
            }
        }

        $arr_bids = [];
        if (!empty($arr_bid_ids)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id IN (' . implode(',', $arr_bid_ids) . ')');
            while ($row = $result->fetch()) {
                $arr_bids[$row['id']] = $row;
            }
        }

        foreach ($petition_data as $bid_data) {
            if (isset($arr_petitions[$bid_data['petition_row_id']]) && isset($arr_bids[$bid_data['bid_id']])) {
                $petition = $arr_petitions[$bid_data['petition_row_id']];
                $row = $arr_bids[$bid_data['bid_id']];

                $array_data = array_merge($row, $petition);
                $array_data['title'] = $array_data['goi_thau'];
                $array_data['title_a'] = nv_htmlspecialchars($array_data['goi_thau']);

                $tbmt = explode('-', $array_data['so_tbmt']);
                $array_codetbmt = $tbmt[0];

                $tbmt_t = getUrlByLanguage('tbmt');
                if ($site_lang != 'vi') {
                    $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['bid_id'] . '.html';
                } else {
                    $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['bid_id'] . '.html';
                }

                $xtpl->assign('DATA', $array_data);

                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_petition_tbmt'), $array_data['title']),
                    'en' => sprintf(get_lang('en', 'title_petition_tbmt'), $array_data['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?tb=' . $array_data['bid_id'],
                    'en' => URL_RE . 'en/?tb=' . $array_data['bid_id']
                ];

                $arrInform['nv_theme_petition_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                $petition_data = [
                    'petition_name' => $petition['req_no'],
                    'petition_content' => $petition['req_content'],
                    'sign_req_date' => nv_date('H:i d/m/Y', $petition['req_date']),
                    'sign_res_date' => !empty($petition['res_date']) ? nv_date('H:i d/m/Y', $petition['res_date']) : '',
                    'petition_response' => $petition['res_content']
                ];

                $xtpl->assign('PETITION', $petition_data);
                if ($petition['is_reply'] == 2) {
                    // Trả lời bổ sung
                    $xtpl->parse('main.has_additional_response');
                } elseif ($petition['is_reply'] == 1) {
                    // Đã có trả lời
                    $xtpl->parse('main.has_response');
                } else {
                    // Kiến nghị mới
                    $xtpl->parse('main.new_petition');
                }
            }
        }
    }

    // Lấy thông tin follow một lần
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt));
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_reoffer_result_mail($array_result, $userid, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;

    if (!empty($array_result)) {
        $xtpl = new XTemplate('email_follow_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);

        $list_result = [];
        $array_codetbmt = '';

        $reoffer_ids = [];
        foreach ($array_result as $bid_id => $bid_data) {
            if (!empty($bid_data['reoffer_result_id'])) {
                $reoffer_ids[] = $bid_data['reoffer_result_id'];
            }
        }

        if (!empty($reoffer_ids)) {
            $reoffer_ids = array_unique($reoffer_ids);
            $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_bidding_reoffer WHERE id IN (' . implode(',', $reoffer_ids) . ')';

            $result = $db->query($sql);

            while ($row = $result->fetch()) {
                $list_result[$row['id']] = $row;
                $tbmt = explode('-', $row['so_tbmt']);
                $array_codetbmt = $tbmt[0];
            }
        }

        // Chỉ lấy đại diện 1 tin theo dõi
        $mail_footer_follow = '';
        if (!empty($array_codetbmt)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
            if ($row = $result->fetch()) {
                $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
            }
        }

        if (!empty($list_result)) {
            foreach ($list_result as $data) {
                // Format dữ liệu
                $data['thoi_diem_bat_dau'] = !empty($data['thoi_diem_bat_dau']) ? nv_date('H:i d/m/Y', $data['thoi_diem_bat_dau']) : '';
                $data['thoi_diem_ket_thuc'] = !empty($data['thoi_diem_ket_thuc']) ? nv_date('H:i d/m/Y', $data['thoi_diem_ket_thuc']) : '';

                // Format giá gói thầu
                if ($data['gia_goi_thau'] > 0) {
                    $data['gia_goi_thau_formatted'] = number_format($data['gia_goi_thau'], 0, ',', '.') . ' VND';
                }

                // Tạo link xem chi tiết
                if ($site_lang != 'vi') {
                    $link_view = NV_MY_DOMAIN . '/' . $site_lang . '/reoffer/' . $data['alias'] . '-' . $data['id'] . '.html';
                } else {
                    $link_view = NV_MY_DOMAIN . '/reoffer/' . $data['alias'] . '-' . $data['id'] . '.html';
                }

                $arrMess = [
                    'vi' => sprintf('%s: %s', get_lang('vi', 'reoffer_result'), $data['so_tbmt']),
                    'en' => sprintf('%s: %s', get_lang('en', 'reoffer_result'), $data['so_tbmt'])
                ];

                $arrLink = [
                    'vi' => $link_view,
                    'en' => $link_view
                ];

                $arrInform['nv_theme_reoffer_result_mail'][] = [
                    'vip_id' => $userid,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                $data['link_view'] = $link_view;
                $xtpl->assign('DATA', $data);

                // Parse các trường có điều kiện
                // Bỏ chu_dau_tu vì bảng bidding_reoffer không có cột này
                if ($data['gia_goi_thau'] > 0) {
                    $xtpl->parse('main.follow.content_reoffer.gia_goi_thau');
                }
                if ($data['so_luong_nha_thau'] > 0) {
                    $xtpl->parse('main.follow.content_reoffer.so_luong_nha_thau');
                }

                $xtpl->parse('main.follow.content_reoffer');
            }
            $xtpl->parse('main.follow');
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_reoffer_room_mail($array_room, $userid, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;

    if (!empty($array_room)) {
        $xtpl = new XTemplate('email_follow_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);

        $list_room = [];
        $array_codetbmt = '';

        $room_ids = [];
        foreach ($array_room as $bid_id => $bid_data) {
            if (!empty($bid_data['reoffer_room_id'])) {
                $room_ids[] = $bid_data['reoffer_room_id'];
            }
        }

        if (!empty($room_ids)) {
            $room_ids = array_unique($room_ids);
            $sql = 'SELECT rr.*, row.so_tbmt, row.goi_thau, row.ben_moi_thau, row.alias
                    FROM nv4_bidding_reoffer_room rr
                    LEFT JOIN ' . NV_PREFIXLANG . '_bidding_row row ON rr.id_detail = row.id
                    WHERE rr.id IN (' . implode(',', $room_ids) . ')';

            $result = $db->query($sql);

            while ($row = $result->fetch()) {

                $list_room[$row['id']] = $row;
                if (!empty($row['so_tbmt'])) {
                    $tbmt = explode('-', $row['so_tbmt']);
                    $array_codetbmt = $tbmt[0];
                }
            }
        }

        // Chỉ lấy đại diện 1 tin theo dõi
        $mail_footer_follow = '';
        if (!empty($array_codetbmt)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
            if ($row = $result->fetch()) {
                $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
            }
        }

        if (!empty($list_room)) {
            foreach ($list_room as $data) {
                // Format dữ liệu
                $data['thoi_gian_chao_gia'] = !empty($data['thoi_gian_chao_gia']) ? nv_date('H:i d/m/Y', $data['thoi_gian_chao_gia']) : '';

                // Format giá dự thầu
                if ($data['gia_du_thau'] > 0) {
                    $data['gia_du_thau_formatted'] = number_format($data['gia_du_thau'], 0, ',', '.') . ' VND';
                }

                // Tạo link xem chi tiết - sử dụng alias từ bidding_row và id từ reoffer_room
                if (!empty($data['alias']) && !empty($data['id'])) {
                    if ($site_lang != 'vi') {
                        $link_view = NV_MY_DOMAIN . '/' . $site_lang . '/reoffer-room/' . $data['alias'] . '-' . $data['id_detail'] . '.html';
                    } else {
                        $link_view = NV_MY_DOMAIN . '/reoffer-room/' . $data['alias'] . '-' . $data['id_detail'] . '.html';
                    }
                }

                $arrMess = [
                    'vi' => sprintf('%s: %s', get_lang('vi', 'reoffer_room'), $data['so_tbmt']),
                    'en' => sprintf('%s: %s', get_lang('en', 'reoffer_room'), $data['so_tbmt'])
                ];

                $arrLink = [
                    'vi' => $link_view,
                    'en' => $link_view
                ];

                $arrInform['nv_theme_reoffer_room_mail'][] = [
                    'vip_id' => $userid,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                $data['link_view'] = $link_view;
                $xtpl->assign('DATA', $data);

                // Parse các trường có điều kiện
                // Bỏ chu_dau_tu vì bảng bidding_row không có cột này
                if (!empty($data['lotno'])) {
                    $xtpl->parse('main.follow.content_reoffer_room.lotno');
                }
                if (!empty($data['lotname'])) {
                    $xtpl->parse('main.follow.content_reoffer_room.lotname');
                }
                if ($data['gia_du_thau'] > 0) {
                    $xtpl->parse('main.follow.content_reoffer_room.gia_du_thau');
                }
                if ($data['thu_tu'] > 0) {
                    $xtpl->parse('main.follow.content_reoffer_room.thu_tu');
                }

                $xtpl->parse('main.follow.content_reoffer_room');
            }
            $xtpl->parse('main.follow');
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}